package com.ict.ycwl.pathcalculate.algorithm.core;

import com.ict.ycwl.pathcalculate.algorithm.dto.PathPlanningRequest;
import com.ict.ycwl.pathcalculate.algorithm.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据预处理器
 * 将原始请求数据转换为算法内部的数据结构
 */
@Slf4j
@Component
public class DataPreprocessor {
    
    /**
     * 预处理请求数据
     * @param request 原始请求数据
     * @return 算法执行上下文
     */
    public AlgorithmContext preprocess(PathPlanningRequest request) {
        log.info("开始数据预处理");
        
        AlgorithmContext context = new AlgorithmContext();
        
        // 设置原始数据
        context.setOriginalAccumulations(request.getAccumulations());
        context.setOriginalTransitDepots(request.getTransitDepots());
        context.setOriginalTeams(request.getTeams());
        context.setTimeMatrix(request.getTimeMatrix());
        
        // 构建索引
        buildIndexes(context);
        
        // 构建分组关系
        buildGroupings(context);
        
        // 数据统计和清洗
        performDataStatistics(context);
        validateTimeMatrix(context); // 🎯 新增：验证时间矩阵数据质量
        fillMissingTimeData(context);

        log.info("数据预处理完成");
        return context;
    }
    
    /**
     * 构建快速查找索引
     */
    private void buildIndexes(AlgorithmContext context) {
        // 构建中转站索引
        Map<Long, TransitDepot> transitDepotIndex = new HashMap<>();
        for (TransitDepot depot : context.getOriginalTransitDepots()) {
            transitDepotIndex.put(depot.getTransitDepotId(), depot);
        }
        context.setTransitDepotIndex(transitDepotIndex);
        
        // 构建班组索引
        Map<Long, Team> teamIndex = new HashMap<>();
        for (Team team : context.getOriginalTeams()) {
            teamIndex.put(team.getTeamId(), team);
        }
        context.setTeamIndex(teamIndex);
        
        // 构建聚集区索引
        Map<Long, Accumulation> accumulationIndex = new HashMap<>();
        for (Accumulation acc : context.getOriginalAccumulations()) {
            accumulationIndex.put(acc.getAccumulationId(), acc);
        }
        context.setAccumulationIndex(accumulationIndex);
        
        log.info("构建索引完成：中转站{}个，班组{}个，聚集区{}个", 
                transitDepotIndex.size(), teamIndex.size(), accumulationIndex.size());
    }
    
    /**
     * 构建分组关系
     */
    private void buildGroupings(AlgorithmContext context) {
        // 按中转站分组聚集区
        Map<Long, List<Accumulation>> depotGroups = context.getOriginalAccumulations()
                .stream()
                .collect(Collectors.groupingBy(Accumulation::getTransitDepotId));
        context.setDepotGroups(depotGroups);
        
        // 按班组分组中转站
        Map<Long, List<TransitDepot>> teamGroups = context.getOriginalTransitDepots()
                .stream()
                .collect(Collectors.groupingBy(TransitDepot::getGroupId));
        context.setTeamGroups(teamGroups);
        
        // 输出分组统计
        log.info("分组统计：");
        for (Map.Entry<Long, List<Accumulation>> entry : depotGroups.entrySet()) {
            TransitDepot depot = context.getTransitDepotById(entry.getKey());
            log.info("  中转站 {} 包含 {} 个聚集区，需规划 {} 条路线", 
                    depot.getTransitDepotName(), 
                    entry.getValue().size(), 
                    depot.getRouteCount());
        }
        
        for (Map.Entry<Long, List<TransitDepot>> entry : teamGroups.entrySet()) {
            Team team = context.getTeamById(entry.getKey());
            log.info("  班组 {} 包含 {} 个中转站", 
                    team.getTeamName(), 
                    entry.getValue().size());
        }
    }
    
    /**
     * 执行数据统计
     */
    private void performDataStatistics(AlgorithmContext context) {
        // 统计配送时间
        List<Double> deliveryTimes = context.getOriginalAccumulations()
                .stream()
                .map(Accumulation::getDeliveryTime)
                .collect(java.util.stream.Collectors.toList());
        
        DoubleSummaryStatistics deliveryTimeStats = deliveryTimes.stream()
                .mapToDouble(Double::doubleValue)
                .summaryStatistics();
        
        // 统计不同配送时间值的分布
        Map<Double, Long> deliveryTimeDistribution = deliveryTimes.stream()
                .collect(java.util.stream.Collectors.groupingBy(t -> t, java.util.stream.Collectors.counting()));
        
        // 检查是否所有配送时间都相同（可能是默认值）
        boolean allSameTime = deliveryTimeDistribution.size() == 1;
        double defaultTime = AlgorithmParameters.DEFAULT_DELIVERY_TIME;
        long defaultTimeCount = deliveryTimeDistribution.getOrDefault(defaultTime, 0L);
        
        log.info("配送时间统计：平均 {}分钟，最小 {}分钟，最大 {}分钟，标准差 {}分钟", 
                String.format("%.1f", deliveryTimeStats.getAverage()), 
                String.format("%.1f", deliveryTimeStats.getMin()), 
                String.format("%.1f", deliveryTimeStats.getMax()),
                String.format("%.1f", calculateStandardDeviation(deliveryTimes)));
        
        if (allSameTime) {
            log.warn("⚠️ 所有聚集区配送时间完全相同 ({}分钟)，可能未使用实际卸货时间数据", 
                    String.format("%.1f", deliveryTimeStats.getAverage()));
        } else {
            log.info("✅ 配送时间数据多样化，共 {} 种不同时间值", deliveryTimeDistribution.size());
        }
        
        if (defaultTimeCount > 0) {
            double defaultRatio = (double)defaultTimeCount / deliveryTimes.size() * 100;
            log.info("使用默认配送时间({}分钟)的聚集区: {} 个 ({:.1f}%)", 
                    String.format("%.1f", defaultTime), defaultTimeCount, defaultRatio);
            
            // 详细报告使用默认配送时间的聚集区（按中转站分组）
            log.info("缺失实际卸货时间的聚集区详情:");
            Map<Long, List<Accumulation>> missingByDepot = context.getOriginalAccumulations()
                    .stream()
                    .filter(acc -> acc.getDeliveryTime() == defaultTime)
                    .collect(java.util.stream.Collectors.groupingBy(Accumulation::getTransitDepotId));
            
            for (Map.Entry<Long, List<Accumulation>> entry : missingByDepot.entrySet()) {
                TransitDepot depot = context.getTransitDepotById(entry.getKey());
                List<Accumulation> missingAccs = entry.getValue();
                
                log.warn("  {} 缺失 {} 个聚集区:", 
                        depot.getTransitDepotName(), missingAccs.size());
                
                // 显示前5个作为示例
                for (int i = 0; i < Math.min(5, missingAccs.size()); i++) {
                    Accumulation acc = missingAccs.get(i);
                    log.warn("    {}. 聚集区{} ({}) - 坐标: ({:.6f}, {:.6f})", 
                            i + 1, acc.getAccumulationId(), acc.getAccumulationName(),
                            acc.getLongitude(), acc.getLatitude());
                }
                
                if (missingAccs.size() > 5) {
                    log.warn("       ... 还有 {} 个聚集区缺失数据", missingAccs.size() - 5);
                }
            }
            
            // 根据缺失比例给出建议
            if (defaultRatio > 80.0) {
                log.error("❌ 严重: 超过80%的聚集区缺失实际卸货时间，建议检查unloading_time表数据");
            } else if (defaultRatio > 50.0) {
                log.warn("⚠️ 警告: 超过50%的聚集区缺失实际卸货时间，算法精度可能受影响");
            } else if (defaultRatio > 20.0) {
                log.info("ℹ️ 提示: {}%的聚集区缺失实际卸货时间，建议完善数据", String.format("%.1f", defaultRatio));
            }
        }
        
        // 统计每个中转站的工作量
        Map<Long, Double> depotWorkloads = new HashMap<>();
        for (Map.Entry<Long, List<Accumulation>> entry : context.getDepotGroups().entrySet()) {
            double totalDeliveryTime = entry.getValue().stream()
                    .mapToDouble(Accumulation::getDeliveryTime)
                    .sum();
            depotWorkloads.put(entry.getKey(), totalDeliveryTime);
            
            TransitDepot depot = context.getTransitDepotById(entry.getKey());
            double avgTimePerRoute = totalDeliveryTime / depot.getRouteCount();
            log.info("  中转站 {} 总配送时间 {}分钟，平均每条路线 {}分钟", 
                    depot.getTransitDepotName(), String.format("%.1f", totalDeliveryTime), String.format("%.1f", avgTimePerRoute));
        }
        
        // 保存统计信息到上下文
        context.getStatistics().put("deliveryTimeStats", deliveryTimeStats);
        context.getStatistics().put("depotWorkloads", depotWorkloads);
        context.getStatistics().put("deliveryTimeDistribution", deliveryTimeDistribution);
    }
    
    /**
     * 计算标准差
     */
    private double calculateStandardDeviation(List<Double> values) {
        if (values.isEmpty()) return 0.0;
        
        double mean = values.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double variance = values.stream()
                .mapToDouble(Double::doubleValue)
                .map(v -> Math.pow(v - mean, 2))
                .average()
                .orElse(0.0);
        
        return Math.sqrt(variance);
    }
    
    /**
     * 填充缺失的时间数据 - 仅限中转站内部，符合算法约束
     */
    private void fillMissingTimeData(AlgorithmContext context) {
        log.info("检查并填充缺失的时间矩阵数据(仅中转站内部)");
        
        // 按中转站分组聚集区
        Map<Long, List<Accumulation>> depotAccumulations = new HashMap<>();
        for (Accumulation acc : context.getOriginalAccumulations()) {
            depotAccumulations.computeIfAbsent(acc.getTransitDepotId(), k -> new ArrayList<>()).add(acc);
        }
        
        // 构建中转站ID到对象的映射
        Map<Long, TransitDepot> depotMap = new HashMap<>();
        for (TransitDepot depot : context.getOriginalTransitDepots()) {
            depotMap.put(depot.getTransitDepotId(), depot);
        }
        
        int totalMissingCount = 0;
        int totalEstimatedCount = 0;
        
        // 按中转站填充时间矩阵（只在中转站内部）
        for (Map.Entry<Long, List<Accumulation>> entry : depotAccumulations.entrySet()) {
            Long depotId = entry.getKey();
            List<Accumulation> accumulations = entry.getValue();
            TransitDepot depot = depotMap.get(depotId);
            
            if (depot == null) continue;
            
            // 该中转站内的所有坐标点（中转站 + 聚集区）
            List<CoordinatePoint> depotPoints = new ArrayList<>();
            
            // 添加中转站坐标
            depotPoints.add(new CoordinatePoint(depot.getLongitude(), depot.getLatitude()));
            
            // 添加该中转站的聚集区坐标
            for (Accumulation acc : accumulations) {
                depotPoints.add(new CoordinatePoint(acc.getLongitude(), acc.getLatitude()));
            }
            
            int depotMissingCount = 0;
            int depotEstimatedCount = 0;
            
            // 检查该中转站内所有点对之间的时间数据
            for (int i = 0; i < depotPoints.size(); i++) {
                for (int j = 0; j < depotPoints.size(); j++) {
                    if (i != j) {
                        CoordinatePoint from = depotPoints.get(i);
                        CoordinatePoint to = depotPoints.get(j);
                        String key = String.format("%.6f,%.6f->%.6f,%.6f", 
                                from.getLongitude(), from.getLatitude(),
                                to.getLongitude(), to.getLatitude());
                        
                        if (!context.getTimeMatrix().containsKey(key)) {
                            depotMissingCount++;
                            
                            // 使用Haversine公式估算缺失的时间数据
                            double estimatedTime = estimateTravelTime(from, to);
                            
                            TimeInfo timeInfo = TimeInfo.builder()
                                    .fromLongitude(from.getLongitude())
                                    .fromLatitude(from.getLatitude())
                                    .toLongitude(to.getLongitude())
                                    .toLatitude(to.getLatitude())
                                    .travelTime(estimatedTime)
                                    .build();
                            
                            context.getTimeMatrix().put(key, timeInfo);
                            depotEstimatedCount++;
                        }
                    }
                }
            }
            
            totalMissingCount += depotMissingCount;
            totalEstimatedCount += depotEstimatedCount;
            
            if (depotMissingCount > 0) {
                log.debug("中转站 {} 缺失 {} 条时间数据，已估算补全 {} 条", 
                        depot.getTransitDepotName(), depotMissingCount, depotEstimatedCount);
            }
        }
        
        if (totalMissingCount > 0) {
            log.warn("时间矩阵缺失 {} 条数据(仅中转站内部)，已使用Haversine公式估算补全 {} 条", 
                    totalMissingCount, totalEstimatedCount);
        } else {
            log.info("时间矩阵数据完整(仅中转站内部)，无需补全");
        }
    }
    
    /**
     * 使用Haversine公式估算行驶时间
     * @param from 起点
     * @param to 终点
     * @return 估算的行驶时间（分钟）
     */
    private double estimateTravelTime(CoordinatePoint from, CoordinatePoint to) {
        // Haversine公式计算距离（千米）
        double earthRadius = 6371.0; // 地球半径（千米）
        
        double lat1Rad = Math.toRadians(from.getLatitude());
        double lat2Rad = Math.toRadians(to.getLatitude());
        double deltaLatRad = Math.toRadians(to.getLatitude() - from.getLatitude());
        double deltaLngRad = Math.toRadians(to.getLongitude() - from.getLongitude());
        
        double a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
                   Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                   Math.sin(deltaLngRad / 2) * Math.sin(deltaLngRad / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        double distance = earthRadius * c;
        
        // 假设平均速度为40km/h，转换为分钟
        double averageSpeed = 40.0; // km/h
        double timeInHours = distance / averageSpeed;
        double timeInMinutes = timeInHours * 60;
        
        // 加上一些额外时间（红绿灯、路况等）
        double extraTime = Math.min(distance * 0.5, 10.0); // 最多10分钟额外时间
        
        return Math.max(timeInMinutes + extraTime, 1.0); // 最少1分钟
    }

    /**
     * 验证时间矩阵数据质量 - 🎯 新增：检查时间矩阵的完整性和有效性
     */
    private void validateTimeMatrix(AlgorithmContext context) {
        Map<String, TimeInfo> timeMatrix = context.getTimeMatrix();

        if (timeMatrix == null || timeMatrix.isEmpty()) {
            log.error("❌ 时间矩阵为空，算法将无法正常工作！");
            return;
        }

        log.info("🔍 开始验证时间矩阵数据质量");

        // 统计基本信息
        int totalEntries = timeMatrix.size();
        int validEntries = 0;
        int invalidEntries = 0;
        double minTime = Double.MAX_VALUE;
        double maxTime = Double.MIN_VALUE;
        double totalTime = 0.0;

        // 统计键格式问题
        int correctFormatKeys = 0;
        int incorrectFormatKeys = 0;

        for (Map.Entry<String, TimeInfo> entry : timeMatrix.entrySet()) {
            String key = entry.getKey();
            TimeInfo timeInfo = entry.getValue();

            // 检查键格式
            if (key.matches("^-?\\d+\\.\\d{6},-?\\d+\\.\\d{6}->-?\\d+\\.\\d{6},-?\\d+\\.\\d{6}$")) {
                correctFormatKeys++;
            } else {
                incorrectFormatKeys++;
                if (incorrectFormatKeys <= 3) { // 只记录前3个格式错误的键
                    log.warn("⚠️ 发现格式不正确的时间矩阵键: {}", key);
                }
            }

            // 检查时间信息有效性
            if (timeInfo != null && timeInfo.getTravelTime() != null && timeInfo.getTravelTime() > 0) {
                validEntries++;
                double time = timeInfo.getTravelTime();
                minTime = Math.min(minTime, time);
                maxTime = Math.max(maxTime, time);
                totalTime += time;
            } else {
                invalidEntries++;
                if (invalidEntries <= 5) { // 只记录前5个无效条目
                    log.warn("⚠️ 发现无效时间矩阵条目: key={}, timeInfo={}", key, timeInfo);
                }
            }
        }

        double validRate = (validEntries * 100.0 / totalEntries);
        double avgTime = validEntries > 0 ? (totalTime / validEntries) : 0.0;
        double keyFormatRate = (correctFormatKeys * 100.0 / totalEntries);

        // 输出验证报告
        log.info("📊 时间矩阵验证报告:");
        log.info("   总条目数: {}", totalEntries);
        log.info("   有效条目: {} ({:.2f}%)", validEntries, validRate);
        log.info("   无效条目: {}", invalidEntries);
        log.info("   键格式正确: {} ({:.2f}%)", correctFormatKeys, keyFormatRate);
        log.info("   键格式错误: {}", incorrectFormatKeys);

        if (validEntries > 0) {
            log.info("   时间范围: {:.2f} - {:.2f} 分钟", minTime, maxTime);
            log.info("   平均时间: {:.2f} 分钟", avgTime);
        }

        // 根据验证结果给出建议
        if (validRate < 50.0) {
            log.error("❌ 严重: 时间矩阵有效率过低 ({:.2f}%)，算法可能无法正常工作", validRate);
        } else if (validRate < 80.0) {
            log.warn("⚠️ 警告: 时间矩阵有效率较低 ({:.2f}%)，可能影响算法精度", validRate);
        } else if (validRate < 95.0) {
            log.info("ℹ️ 提示: 时间矩阵有效率 {:.2f}%，建议进一步完善数据", validRate);
        } else {
            log.info("✅ 时间矩阵数据质量良好 ({:.2f}%有效)", validRate);
        }

        if (keyFormatRate < 95.0) {
            log.warn("⚠️ 发现 {} 个键格式不正确的条目，可能导致查找失败", incorrectFormatKeys);
        }

        // 保存验证统计到上下文
        context.getStatistics().put("timeMatrixTotalEntries", totalEntries);
        context.getStatistics().put("timeMatrixValidEntries", validEntries);
        context.getStatistics().put("timeMatrixValidRate", validRate);
        context.getStatistics().put("timeMatrixKeyFormatRate", keyFormatRate);
    }
}