package com.ict.ycwl.pathcalculate;

import com.ict.ycwl.pathcalculate.algorithm.core.UnifiedTimeCalculationService;
import com.ict.ycwl.pathcalculate.algorithm.entity.CoordinatePoint;
import com.ict.ycwl.pathcalculate.algorithm.entity.TimeInfo;
import com.ict.ycwl.pathcalculate.service.adapter.DatabaseToAlgorithmAdapter;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

/**
 * 时间矩阵修复效果测试
 * 验证修复后的时间矩阵能正确匹配和查找
 */
@Slf4j
@SpringBootTest
public class TimeMatrixFixTest {

    @Autowired
    private UnifiedTimeCalculationService timeCalculationService;

    @Autowired
    private DatabaseToAlgorithmAdapter databaseAdapter;

    @Test
    public void testTimeMatrixKeyFormat() {
        log.info("🧪 测试时间矩阵键格式修复效果");
        
        // 创建测试时间矩阵
        Map<String, TimeInfo> timeMatrix = new HashMap<>();
        
        // 使用标准6位小数精度格式
        String key1 = "113.596766,24.810403->113.598123,24.812456";
        TimeInfo timeInfo1 = TimeInfo.builder()
                .fromLongitude(113.596766)
                .fromLatitude(24.810403)
                .toLongitude(113.598123)
                .toLatitude(24.812456)
                .travelTime(3.5)
                .build();
        timeMatrix.put(key1, timeInfo1);
        
        String key2 = "113.598123,24.812456->113.352817,25.128375";
        TimeInfo timeInfo2 = TimeInfo.builder()
                .fromLongitude(113.598123)
                .fromLatitude(24.812456)
                .toLongitude(113.352817)
                .toLatitude(25.128375)
                .travelTime(45.2)
                .build();
        timeMatrix.put(key2, timeInfo2);
        
        log.info("✅ 创建测试时间矩阵，包含 {} 条记录", timeMatrix.size());
        
        // 测试时间矩阵验证功能
        String validationResult = timeCalculationService.validateTimeMatrix(timeMatrix);
        log.info("📊 时间矩阵验证结果: {}", validationResult);
        
        // 测试坐标查找
        CoordinatePoint from = new CoordinatePoint(113.596766, 24.810403);
        CoordinatePoint to = new CoordinatePoint(113.598123, 24.812456);
        
        double travelTime = timeCalculationService.getTravelTime(from, to, timeMatrix);
        log.info("🎯 查找结果: {} -> {} = {} 分钟", 
                formatCoordinate(from), formatCoordinate(to), travelTime);
        
        if (travelTime == 3.5) {
            log.info("✅ 时间矩阵查找成功！修复生效");
        } else {
            log.error("❌ 时间矩阵查找失败！期望3.5分钟，实际{}分钟", travelTime);
        }
        
        // 测试反向查找
        double reverseTravelTime = timeCalculationService.getTravelTime(to, from, timeMatrix);
        log.info("🔄 反向查找结果: {} -> {} = {} 分钟", 
                formatCoordinate(to), formatCoordinate(from), reverseTravelTime);
        
        // 测试不存在的路径
        CoordinatePoint nonExistent = new CoordinatePoint(114.0, 25.0);
        double estimatedTime = timeCalculationService.getTravelTime(from, nonExistent, timeMatrix);
        log.info("🔄 距离估算结果: {} -> {} = {} 分钟", 
                formatCoordinate(from), formatCoordinate(nonExistent), estimatedTime);
    }

    @Test
    public void testDatabaseTimeMatrixLoading() {
        log.info("🧪 测试数据库时间矩阵加载");
        
        try {
            // 这个测试需要数据库连接，如果没有连接会跳过
            var request = databaseAdapter.loadFromDatabase();
            var timeMatrix = request.getTimeMatrix();
            
            log.info("📊 从数据库加载时间矩阵: {} 条记录", timeMatrix.size());
            
            // 验证数据质量
            String validationResult = timeCalculationService.validateTimeMatrix(timeMatrix);
            log.info("📊 数据库时间矩阵验证结果: {}", validationResult);
            
            // 测试几个随机查找
            int testCount = 0;
            int hitCount = 0;
            
            for (Map.Entry<String, TimeInfo> entry : timeMatrix.entrySet()) {
                if (testCount >= 10) break; // 只测试前10个
                
                String key = entry.getKey();
                TimeInfo timeInfo = entry.getValue();
                
                if (timeInfo != null && timeInfo.getTravelTime() != null) {
                    // 解析坐标
                    String[] parts = key.split("->");
                    if (parts.length == 2) {
                        String[] fromParts = parts[0].split(",");
                        String[] toParts = parts[1].split(",");
                        
                        if (fromParts.length == 2 && toParts.length == 2) {
                            try {
                                CoordinatePoint from = new CoordinatePoint(
                                    Double.parseDouble(fromParts[0]), 
                                    Double.parseDouble(fromParts[1])
                                );
                                CoordinatePoint to = new CoordinatePoint(
                                    Double.parseDouble(toParts[0]), 
                                    Double.parseDouble(toParts[1])
                                );
                                
                                double foundTime = timeCalculationService.getTravelTime(from, to, timeMatrix);
                                
                                if (Math.abs(foundTime - timeInfo.getTravelTime()) < 0.01) {
                                    hitCount++;
                                    log.debug("✅ 查找命中: {} = {} 分钟", key, foundTime);
                                } else {
                                    log.warn("⚠️ 查找不匹配: {} 期望{} 实际{}", key, timeInfo.getTravelTime(), foundTime);
                                }
                                
                                testCount++;
                            } catch (NumberFormatException e) {
                                log.warn("⚠️ 坐标解析失败: {}", key);
                            }
                        }
                    }
                }
            }
            
            double hitRate = testCount > 0 ? (hitCount * 100.0 / testCount) : 0.0;
            log.info("📊 查找测试结果: {}/{} 命中，命中率 {:.1f}%", hitCount, testCount, hitRate);
            
            if (hitRate >= 90.0) {
                log.info("✅ 时间矩阵查找功能正常！");
            } else {
                log.warn("⚠️ 时间矩阵查找命中率较低，可能存在问题");
            }
            
        } catch (Exception e) {
            log.warn("⚠️ 数据库连接失败，跳过数据库测试: {}", e.getMessage());
        }
    }
    
    private String formatCoordinate(CoordinatePoint point) {
        return String.format("(%.6f,%.6f)", point.getLongitude(), point.getLatitude());
    }
}
